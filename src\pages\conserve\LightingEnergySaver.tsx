import React, { useState } from 'react';
import {
  Building2,
  Zap,
  Users,
  BarChart3,
  Shield,
  Smartphone,
  Clock,
  DollarSign,
  CheckCircle,
  ArrowRight,
  Home,
  Wrench,
  Building,
  TrendingUp,
  Bell,
  CreditCard,
  Gauge,
  Bot,
  Eye,
  Briefcase
} from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

const TenantBillingPage = () => {
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      title: "Tenant Management",
      description: "Comprehensive tenant profiles with lease agreements, contact info, and multi-property consolidation into single bills."
    },
    {
      title: "Automated Meter Reading",
      description: "Smart meter integration for automatic utility consumption data collection and error-free invoice generation."
    },
    {
      title: "Anomaly Detection",
      description: "AI-powered trend analysis with instant alerts for abnormalities and consumption optimization opportunities."
    },
    {
      title: "Real-time Visibility",
      description: "Live utility consumption monitoring with automated invoice generation based on configured service periods."
    },
    {
      title: "Payment Tracking",
      description: "Support for both pre-paid and post-paid options with comprehensive payment history tracking."
    },
    {
      title: "Analytics & Reporting",
      description: "Detailed online and historical reports with analytics on billing data and outstanding payments."
    }
  ];

  const benefits = [
    { title: "Increased Efficiency", desc: "Automate meter readings and invoice generation" },
    { title: "Reduced Errors", desc: "Minimize billing and payment mistakes" },
    { title: "Improved Cash Flow", desc: "Faster payment tracking and reduced late payments" },
    { title: "Better Tenant Relations", desc: "Accurate billing improves satisfaction" },
    { title: "Cost Savings", desc: "Reduce manual billing overhead costs" }
  ];

  const industries = [
    { title: "Housing Associations" },
    { title: "Commercial Establishments" },
    { title: "Utility Managing Company" },
    { title: "Facility Managers" },
    { title: "Property Management" },
    { title: "Staff Quarters Management" }
  ];

  return (
    <PageLayout
      title="Lighting Energy Saver"
      subtitle="Smart Tenant Billing Solution"
      category="conserve"
    >
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-green-900 via-emerald-800 to-teal-900">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-repeat" style={{
            backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)',
            backgroundSize: '20px 20px'
          }}></div>
        </div>

        <div className="relative container mx-auto px-6 py-20 z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="inline-flex items-center px-4 py-2 bg-green-500 bg-opacity-20 backdrop-blur-sm rounded-full border border-green-400 border-opacity-30">
                  <Zap className="w-4 h-4 text-green-300 mr-2" />
                  <span className="text-green-100 text-sm font-medium">Smart Utility Management</span>
                </div>

                <h1 className="text-5xl lg:text-7xl font-bold text-white leading-tight">
                  Smart Tenant
                  <span className="block bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                    Billing Solution
                  </span>
                </h1>

                <p className="text-xl text-green-100 opacity-90 leading-relaxed max-w-2xl">
                  Scale your business and drive tenant satisfaction with our seamless,
                  stress-free utility billing system for residential and commercial establishments.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="group px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-xl">
                    Get Started Today
                    <ArrowRight className="inline-block ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </button>
                  <button className="px-8 py-4 border-2 border-green-400 border-opacity-50 text-green-100 rounded-xl font-semibold hover:bg-green-500 hover:bg-opacity-20 transition-all duration-300 backdrop-blur-sm">
                    Schedule Demo
                  </button>
                </div>
              </div>

              <div className="relative">
                <div className="relative z-10 bg-white bg-opacity-10 backdrop-blur-lg rounded-3xl p-8 border border-white border-opacity-20">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="bg-gradient-to-br from-green-500 from-opacity-20 to-emerald-500 to-opacity-20 rounded-2xl p-6 backdrop-blur-sm border border-green-400 border-opacity-30">
                      <Building2 className="w-8 h-8 text-green-300 mb-3" />
                      <h3 className="text-white font-semibold mb-2">Multi-Property</h3>
                      <p className="text-green-100 opacity-80 text-sm">Consolidate multiple meters</p>
                    </div>
                    <div className="bg-gradient-to-br from-emerald-500 from-opacity-20 to-teal-500 to-opacity-20 rounded-2xl p-6 backdrop-blur-sm border border-emerald-400 border-opacity-30">
                      <Smartphone className="w-8 h-8 text-emerald-300 mb-3" />
                      <h3 className="text-white font-semibold mb-2">AI Self-Care</h3>
                      <p className="text-emerald-100 opacity-80 text-sm">Mobile app with AI chatbot</p>
                    </div>
                    <div className="bg-gradient-to-br from-teal-500 from-opacity-20 to-cyan-500 to-opacity-20 rounded-2xl p-6 backdrop-blur-sm border border-teal-400 border-opacity-30">
                      <BarChart3 className="w-8 h-8 text-teal-300 mb-3" />
                      <h3 className="text-white font-semibold mb-2">Real-time Analytics</h3>
                      <p className="text-teal-100 opacity-80 text-sm">Live consumption monitoring</p>
                    </div>
                    <div className="bg-gradient-to-br from-cyan-500 from-opacity-20 to-green-500 to-opacity-20 rounded-2xl p-6 backdrop-blur-sm border border-cyan-400 border-opacity-30">
                      <Shield className="w-8 h-8 text-cyan-300 mb-3" />
                      <h3 className="text-white font-semibold mb-2">Automated Security</h3>
                      <p className="text-cyan-100 opacity-80 text-sm">Error-free billing</p>
                    </div>
                  </div>
                </div>
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full opacity-20 blur-xl"></div>
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-full opacity-20 blur-xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Industries Section */}
      <section className="py-20 bg-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-green-50 from-opacity-50 to-emerald-50 to-opacity-50"></div>
        <div className="relative container mx-auto px-6 z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Ideal for <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Every Industry</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our tenant billing solution adapts to various business models and property management needs
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Home className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">Housing Associations</h3>
              <div className="h-1 w-0 bg-gradient-to-r from-green-500 to-emerald-500 group-hover:w-full transition-all duration-500 rounded-full"></div>
            </div>

            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Building className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">Commercial Establishments</h3>
              <div className="h-1 w-0 bg-gradient-to-r from-emerald-500 to-teal-500 group-hover:w-full transition-all duration-500 rounded-full"></div>
            </div>

            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">Utility Managing Company</h3>
              <div className="h-1 w-0 bg-gradient-to-r from-teal-500 to-cyan-500 group-hover:w-full transition-all duration-500 rounded-full"></div>
            </div>

            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-emerald-700 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Wrench className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">Facility Managers</h3>
              <div className="h-1 w-0 bg-gradient-to-r from-green-600 to-emerald-600 group-hover:w-full transition-all duration-500 rounded-full"></div>
            </div>

            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-700 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Building2 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">Property Management</h3>
              <div className="h-1 w-0 bg-gradient-to-r from-emerald-600 to-teal-600 group-hover:w-full transition-all duration-500 rounded-full"></div>
            </div>

            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-br from-teal-600 to-green-700 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Briefcase className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">Staff Quarters Management</h3>
              <div className="h-1 w-0 bg-gradient-to-r from-teal-600 to-green-600 group-hover:w-full transition-all duration-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-green-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Powerful <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Features</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive tools designed to streamline your tenant billing process
            </p>
          </div>

          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className={`p-6 rounded-2xl cursor-pointer transition-all duration-300 ${
                      activeFeature === index
                        ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-2xl transform scale-105'
                        : 'bg-white hover:bg-green-50 shadow-lg hover:shadow-xl'
                    }`}
                    onClick={() => setActiveFeature(index)}
                  >
                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-xl ${
                        activeFeature === index
                          ? 'bg-white bg-opacity-20'
                          : 'bg-gradient-to-br from-green-500 to-emerald-600'
                      }`}>
                        {index === 0 && <Users className="w-6 h-6 text-white" />}
                        {index === 1 && <Gauge className="w-6 h-6 text-white" />}
                        {index === 2 && <Bell className="w-6 h-6 text-white" />}
                        {index === 3 && <Eye className="w-6 h-6 text-white" />}
                        {index === 4 && <CreditCard className="w-6 h-6 text-white" />}
                        {index === 5 && <BarChart3 className="w-6 h-6 text-white" />}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                        <p className={`${activeFeature === index ? 'text-green-100' : 'text-gray-600'}`}>
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="relative">
                <div className="bg-gradient-to-br from-green-600 to-emerald-700 rounded-3xl p-12 text-white relative overflow-hidden">
                  <div className="absolute top-4 right-4 w-32 h-32 bg-white bg-opacity-10 rounded-full blur-2xl"></div>
                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-green-500 bg-opacity-20 rounded-2xl flex items-center justify-center mb-6">
                      {activeFeature === 0 && <Users className="w-8 h-8 text-green-200" />}
                      {activeFeature === 1 && <Gauge className="w-8 h-8 text-green-200" />}
                      {activeFeature === 2 && <Bell className="w-8 h-8 text-green-200" />}
                      {activeFeature === 3 && <Eye className="w-8 h-8 text-green-200" />}
                      {activeFeature === 4 && <CreditCard className="w-8 h-8 text-green-200" />}
                      {activeFeature === 5 && <BarChart3 className="w-8 h-8 text-green-200" />}
                    </div>
                    <h3 className="text-2xl font-bold mb-4">{features[activeFeature].title}</h3>
                    <p className="text-green-100 text-lg leading-relaxed">
                      {features[activeFeature].description}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Why Choose <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Alensoft</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the benefits of our comprehensive tenant billing solution
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <div className="group">
              <div className="bg-gradient-to-br from-white to-green-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-green-100 border-opacity-50">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Clock className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">Increased Efficiency</h3>
                <p className="text-gray-600 leading-relaxed">Automate meter readings and invoice generation</p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-white to-green-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-green-100 border-opacity-50">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">Reduced Errors</h3>
                <p className="text-gray-600 leading-relaxed">Minimize billing and payment mistakes</p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-white to-green-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-green-100 border-opacity-50">
                <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">Improved Cash Flow</h3>
                <p className="text-gray-600 leading-relaxed">Faster payment tracking and reduced late payments</p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-white to-green-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-green-100 border-opacity-50">
                <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-emerald-700 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">Better Tenant Relations</h3>
                <p className="text-gray-600 leading-relaxed">Accurate billing improves satisfaction</p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-white to-green-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-green-100 border-opacity-50">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-700 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">Cost Savings</h3>
                <p className="text-gray-600 leading-relaxed">Reduce manual billing overhead costs</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Self-Care Section */}
      <section className="py-20 bg-gradient-to-br from-green-900 via-emerald-800 to-teal-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-400 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-emerald-400 rounded-full blur-3xl"></div>
        </div>

        <div className="relative container mx-auto px-6 z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-flex items-center px-4 py-2 bg-green-500 bg-opacity-20 backdrop-blur-sm rounded-full border border-green-400 border-opacity-30 mb-8">
                  <Bot className="w-4 h-4 text-green-300 mr-2" />
                  <span className="text-green-100 text-sm font-medium">AI-Powered Solution</span>
                </div>

                <h2 className="text-4xl lg:text-5xl font-bold mb-6">
                  AI Self-Care
                  <span className="block bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                    Mobile App & Portal
                  </span>
                </h2>

                <p className="text-xl text-green-100 opacity-90 mb-8 leading-relaxed">
                  Give tenants complete control over their costs and consumption with our intelligent
                  self-service platform featuring 24/7 AI chatbot assistance.
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0" />
                    <span className="text-green-100">Customizable insights dashboard</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0" />
                    <span className="text-green-100">Access to online data and previous bills</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0" />
                    <span className="text-green-100">24/7 AI chatbot support</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0" />
                    <span className="text-green-100">Automated inquiry handling</span>
                  </div>
                </div>

                <button className="px-8 py-4 bg-white text-green-800 rounded-xl font-semibold hover:bg-green-50 transition-all duration-300 transform hover:scale-105 shadow-xl">
                  Explore AI Features
                </button>
              </div>

              <div className="relative">
                <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-3xl p-8 border border-white border-opacity-20">
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-green-500 from-opacity-20 to-emerald-500 to-opacity-20 rounded-2xl p-6 backdrop-blur-sm border border-green-400 border-opacity-30">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-white font-semibold">Monthly Usage</h4>
                        <TrendingUp className="w-5 h-5 text-green-300" />
                      </div>
                      <div className="flex items-end space-x-2">
                        <div className="h-8 w-4 bg-green-400 rounded"></div>
                        <div className="h-12 w-4 bg-green-500 rounded"></div>
                        <div className="h-6 w-4 bg-green-300 rounded"></div>
                        <div className="h-10 w-4 bg-emerald-400 rounded"></div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-emerald-500 from-opacity-20 to-teal-500 to-opacity-20 rounded-2xl p-6 backdrop-blur-sm border border-emerald-400 border-opacity-30">
                      <div className="flex items-center space-x-3 mb-4">
                        <Bot className="w-6 h-6 text-emerald-300" />
                        <span className="text-white font-semibold">AI Assistant</span>
                      </div>
                      <p className="text-emerald-100 opacity-80 text-sm">
                        "Your electricity usage is 15% lower this month. Great job on conservation!"
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-gray-50 to-green-50">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
              Ready to Transform Your
              <span className="block bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                Tenant Billing Process?
              </span>
            </h2>

            <p className="text-xl text-gray-600 mb-10 leading-relaxed">
              Join thousands of property managers who trust Alensoft for their utility billing needs.
              Experience the power of automation, accuracy, and AI-driven insights.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <button className="group px-10 py-5 bg-gradient-to-r from-green-600 to-emerald-700 text-white rounded-2xl font-bold text-lg hover:from-green-700 hover:to-emerald-800 transition-all duration-300 transform hover:scale-105 shadow-xl">
                Start Free Trial
                <ArrowRight className="inline-block ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform" />
              </button>
              <button className="px-10 py-5 border-2 border-green-600 text-green-700 rounded-2xl font-bold text-lg hover:bg-green-600 hover:text-white transition-all duration-300">
                Request Demo
              </button>
            </div>

            <div className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">500+</div>
                <div className="text-gray-600">Happy Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">99.9%</div>
                <div className="text-gray-600">Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">24/7</div>
                <div className="text-gray-600">Support</div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </PageLayout>
  );
};

export default TenantBillingPage;