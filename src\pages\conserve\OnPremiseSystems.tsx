import React, { useState } from 'react';
import { Zap, TrendingUp, Shield, Award, Users, Monitor, BarChart3, Leaf, Settings, CheckCircle, ArrowRight, Home, ChevronRight, Calendar, Download, Phone, Mail, Sparkles, Target, Globe } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

const AlensoftEnMSProductPage = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedModule, setSelectedModule] = useState(null);

  const stats = [
    { label: 'Typical Energy Savings', value: '8-15%', icon: TrendingUp, color: 'from-green-400 to-green-600' },
    { label: 'Payback Period', value: '1.5-2 Years', icon: Award, color: 'from-emerald-400 to-emerald-600' },
    { label: 'Customers Served', value: '300+', icon: Users, color: 'from-teal-400 to-teal-600' },
    { label: 'Devices Connected', value: '20,000+', icon: Monitor, color: 'from-green-500 to-green-700' }
  ];

  const addOnModules = [
    {
      title: 'Power Quality Management',
      description: 'Integrate with PQ meters & monitor electrical system performance',
      icon: Zap,
      features: ['Real-time power quality monitoring', 'Harmonic analysis', 'Voltage fluctuation tracking', 'Equipment protection alerts']
    },
    {
      title: 'Advanced Demand Management',
      description: 'Optimize energy use during peak demand periods',
      icon: BarChart3,
      features: ['Peak demand forecasting', 'Load shifting strategies', 'Cost optimization', 'Automated load control']
    },
    {
      title: 'Utility Asset Performance',
      description: 'Maximize the performance & conserve Energy of critical Utility Assets',
      icon: Settings,
      features: ['Asset health monitoring', 'Performance optimization', 'Energy conservation tracking', 'Maintenance scheduling']
    },
    {
      title: 'ISO 50001 Compliance',
      description: 'Meet the requirements of international energy management standards',
      icon: Shield,
      features: ['Compliance documentation', 'Audit trail management', 'Standard reporting', 'Certification support']
    },
    {
      title: 'GHG & Sustainability Reporting',
      description: 'Comprehensive carbon footprint management & Compliance reporting',
      icon: Leaf,
      features: ['Carbon footprint calculation', 'Emission tracking', 'Sustainability metrics', 'Regulatory compliance']
    },
    {
      title: 'Asset Management',
      description: 'Predictive & Planned maintenance of Assets',
      icon: Monitor,
      features: ['Predictive maintenance', 'Asset lifecycle tracking', 'Maintenance scheduling', 'Performance analytics']
    }
  ];

  const coreFeatures = [
    'Real-time energy monitoring and analytics',
    'AI-powered predictive intelligence',
    'Automated energy optimization',
    'Comprehensive reporting dashboard',
    'Multi-site management capabilities',
    'Mobile and web-based access',
    'Integration with existing systems',
    'Custom alert and notification system'
  ];

  const benefits = [
    { title: 'Cost Reduction', description: '8-15% reduction in energy costs through intelligent optimization', icon: TrendingUp },
    { title: 'Quick ROI', description: 'Payback period of just 1.5-2 years with immediate savings', icon: Award },
    { title: 'Sustainability', description: 'Reduce carbon footprint and meet environmental goals', icon: Leaf },
    { title: 'Compliance', description: 'Meet international energy management standards', icon: Shield }
  ];

  return (
    <div className="font-['Open_Sans'] min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <PageLayout
        title="On-Premise Systems"
        subtitle="Smart Energy Management System (EnMS)"
        category="conserve"
      >
        {/* Navigation Breadcrumb */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-green-200/50 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav className="flex items-center space-x-3 text-sm text-gray-700">
              <div className="flex items-center space-x-2 bg-green-100/50 px-3 py-1.5 rounded-full">
                <Home className="w-4 h-4 text-green-600" />
                <span className="font-medium text-green-800">Home</span>
              </div>
              <ChevronRight className="w-4 h-4 text-green-400" />
              <span className="font-medium text-gray-600">Products</span>
              <ChevronRight className="w-4 h-4 text-green-400" />
              <span className="font-medium text-gray-600">Energy Management</span>
              <ChevronRight className="w-4 h-4 text-green-400" />
              <span className="text-green-700 font-bold bg-green-100 px-3 py-1 rounded-full">Smart EnMS</span>
            </nav>
          </div>
        </div>

        {/* Header Section */}
        <div className="relative bg-gradient-to-br from-green-600 via-green-700 to-emerald-800 text-white overflow-hidden">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-green-500/20 via-emerald-600/30 to-teal-700/20"></div>
            <div className="absolute top-10 left-10 w-32 h-32 bg-green-400/10 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-48 h-48 bg-emerald-400/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-teal-400/10 rounded-full blur-lg animate-pulse delay-500"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-28">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="space-y-10">
                <div className="space-y-6">
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-green-300/30 shadow-lg">
                    <Zap className="w-5 h-5 mr-3 text-green-300" />
                    <span className="text-sm font-semibold text-green-100 tracking-wide">AI-POWERED ENERGY MANAGEMENT</span>
                  </div>

                  <h1 className="text-5xl lg:text-6xl font-black leading-tight tracking-tight">
                    <span className="bg-gradient-to-r from-white via-green-100 to-emerald-200 bg-clip-text text-transparent">
                      SMART ENERGY
                    </span>
                    <br />
                    <span className="bg-gradient-to-r from-green-200 via-emerald-300 to-teal-300 bg-clip-text text-transparent">
                      MANAGEMENT
                    </span>
                    <br />
                    <span className="text-3xl lg:text-4xl font-bold text-green-300 mt-3 block">
                      SYSTEM (EnMS)
                    </span>
                  </h1>

                  <p className="text-xl lg:text-2xl text-green-100 leading-relaxed font-medium">
                    Predictive & proactive intelligence with built-in AI & analytics for
                    <span className="text-green-300 font-bold"> energy savings right out of the box</span>
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="group bg-white text-green-700 px-8 py-4 rounded-xl font-bold text-lg hover:bg-green-50 transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                    <Download className="w-6 h-6 mr-3 group-hover:animate-bounce" />
                    Download Brochure
                  </button>
                  <button className="group border-2 border-white text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-white hover:text-green-700 transition-all duration-300 flex items-center justify-center backdrop-blur-sm hover:backdrop-blur-none transform hover:-translate-y-1">
                    <Calendar className="w-6 h-6 mr-3 group-hover:animate-pulse" />
                    Schedule Demo
                  </button>
                </div>
              </div>

              <div className="relative">
                <div className="relative group">
                  <img
                    src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=700&h=500&fit=crop&auto=format"
                    alt="Smart Energy Management Dashboard"
                    className="rounded-2xl shadow-2xl transform rotate-1 group-hover:rotate-0 transition-all duration-700 border-4 border-white/20"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-green-600/30 via-transparent to-transparent rounded-2xl"></div>
                </div>

                {/* Floating Status Cards */}
                <div className="absolute -bottom-6 -left-6 bg-white/95 backdrop-blur-sm p-4 rounded-xl shadow-xl border border-green-100">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse shadow-lg"></div>
                    <span className="text-sm font-bold text-gray-800">Live Monitoring Active</span>
                  </div>
                </div>

                <div className="absolute -top-6 -right-6 bg-green-600/90 backdrop-blur-sm p-4 rounded-xl shadow-xl text-white">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5 text-green-300" />
                    <span className="text-sm font-bold">8-15% Savings</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="py-20 bg-gradient-to-br from-white via-green-50/30 to-emerald-50/50 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-0 left-0 w-full h-full" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-4">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Proven Results
                </span>
              </h2>
              <p className="text-xl text-gray-600 font-medium max-w-2xl mx-auto">
                Join hundreds of satisfied customers achieving remarkable energy savings
              </p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="group">
                    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 text-center shadow-lg hover:shadow-2xl transition-all duration-500 border border-green-100/50 hover:border-green-300/50 transform hover:-translate-y-2">
                      <div className={`inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br ${stat.color} mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                        <IconComponent className="w-10 h-10 text-white" />
                      </div>
                      <div className="text-4xl lg:text-5xl font-black text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                        {stat.value}
                      </div>
                      <div className="text-gray-700 font-bold text-lg">{stat.label}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Product Overview Tabs */}
        <div className="py-24 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-6xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Comprehensive Energy
                </span>
                <br />
                <span className="text-gray-800">Management Solution</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-700 max-w-4xl mx-auto font-medium leading-relaxed">
                Transform how you monitor, manage, and optimize energy resources across your entire operations with our
                <span className="text-green-600 font-bold"> AI-powered platform</span>
              </p>
            </div>

            {/* Tab Navigation */}
            <div className="flex flex-wrap justify-center mb-16 bg-white/80 backdrop-blur-sm rounded-2xl p-3 shadow-2xl max-w-3xl mx-auto border border-green-100/50">
              {[
                { id: 'overview', label: 'Overview', icon: Target },
                { id: 'features', label: 'Core Features', icon: Settings },
                { id: 'benefits', label: 'Benefits', icon: Award },
                { id: 'modules', label: 'Add-on Modules', icon: Globe }
              ].map((tab) => {
                const TabIcon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group flex items-center px-6 py-4 rounded-xl font-bold text-lg transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-xl transform scale-105'
                        : 'text-gray-700 hover:text-green-600 hover:bg-green-50/80 hover:scale-102'
                    }`}
                  >
                    <TabIcon className={`w-5 h-5 mr-2 transition-all ${
                      activeTab === tab.id ? 'text-green-200' : 'text-gray-500 group-hover:text-green-500'
                    }`} />
                    {tab.label}
                  </button>
                );
              })}
            </div>

            {/* Tab Content */}
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl p-10 lg:p-16 border border-green-100/50">
              {activeTab === 'overview' && (
                <div className="grid lg:grid-cols-2 gap-16 items-center">
                  <div className="space-y-8">
                    <div className="space-y-4">
                      <h3 className="text-3xl lg:text-4xl font-black text-gray-900">
                        Why Choose
                        <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent"> Alensoft EnMS?</span>
                      </h3>
                      <p className="text-lg text-gray-700 leading-relaxed font-medium">
                        In today's competitive industrial landscape, optimizing energy consumption isn't just about reducing costs—it's about
                        <span className="text-green-600 font-bold"> sustainability, compliance, and gaining a competitive edge</span>.
                        ALENSOFT EnMS delivers a comprehensive solution that transforms how you monitor, manage, and optimize resources across your entire operations.
                      </p>
                    </div>

                    <div className="space-y-6">
                      <div className="group flex items-start space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 hover:shadow-lg transition-all">
                        <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform">
                          <CheckCircle className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900 text-lg mb-1">Cost Optimization</h4>
                          <p className="text-gray-700 font-medium">Reduce energy costs by 8-15% through intelligent monitoring and optimization.</p>
                        </div>
                      </div>

                      <div className="group flex items-start space-x-4 p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-100 hover:shadow-lg transition-all">
                        <div className="bg-emerald-600 p-2 rounded-lg group-hover:scale-110 transition-transform">
                          <CheckCircle className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900 text-lg mb-1">Sustainability Goals</h4>
                          <p className="text-gray-700 font-medium">Meet environmental targets with comprehensive carbon footprint management.</p>
                        </div>
                      </div>

                      <div className="group flex items-start space-x-4 p-4 bg-gradient-to-r from-teal-50 to-green-50 rounded-xl border border-teal-100 hover:shadow-lg transition-all">
                        <div className="bg-teal-600 p-2 rounded-lg group-hover:scale-110 transition-transform">
                          <CheckCircle className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900 text-lg mb-1">Compliance Ready</h4>
                          <p className="text-gray-700 font-medium">Built-in support for ISO 50001 and international energy standards.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="relative">
                    <div className="relative group">
                      <img
                        src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=700&h=500&fit=crop&auto=format"
                        alt="Energy Management Overview"
                        className="rounded-2xl shadow-2xl group-hover:scale-105 transition-all duration-500 border-4 border-green-100"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-green-600/30 via-transparent to-transparent rounded-2xl"></div>
                    </div>

                    {/* Floating Elements */}
                    <div className="absolute -top-4 -right-4 bg-green-600 text-white p-3 rounded-xl shadow-xl">
                      <div className="flex items-center space-x-2">
                        <Sparkles className="w-5 h-5" />
                        <span className="font-bold text-sm">AI-Powered</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'features' && (
                <div className="space-y-12">
                  <div className="text-center">
                    <h3 className="text-3xl lg:text-4xl font-black text-gray-900 mb-4">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Core Platform Features
                      </span>
                    </h3>
                    <p className="text-xl text-gray-600 font-medium max-w-2xl mx-auto">
                      Powerful capabilities designed to transform your energy management
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {coreFeatures.map((feature, index) => (
                      <div key={index} className="group">
                        <div className="flex items-start space-x-4 p-6 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-2xl border border-green-100 hover:border-green-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                          <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform shadow-lg">
                            <CheckCircle className="w-6 h-6 text-white" />
                          </div>
                          <span className="text-gray-800 font-bold text-lg leading-relaxed">{feature}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'benefits' && (
                <div className="space-y-12">
                  <div className="text-center">
                    <h3 className="text-3xl lg:text-4xl font-black text-gray-900 mb-4">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Key Benefits
                      </span>
                    </h3>
                    <p className="text-xl text-gray-600 font-medium max-w-2xl mx-auto">
                      Discover the transformative advantages of our energy management solution
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8">
                    {benefits.map((benefit, index) => {
                      const IconComponent = benefit.icon;
                      const gradients = [
                        'from-green-500 to-emerald-600',
                        'from-emerald-500 to-teal-600',
                        'from-teal-500 to-green-600',
                        'from-green-600 to-emerald-700'
                      ];
                      return (
                        <div key={index} className="group">
                          <div className="relative p-8 bg-white rounded-2xl border border-green-100 hover:border-green-300 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                            {/* Background Gradient */}
                            <div className={`absolute top-0 left-0 w-full h-2 bg-gradient-to-r ${gradients[index % gradients.length]}`}></div>

                            <div className="flex items-start space-x-6">
                              <div className={`bg-gradient-to-br ${gradients[index % gradients.length]} p-4 rounded-2xl shadow-lg group-hover:scale-110 transition-transform`}>
                                <IconComponent className="w-8 h-8 text-white" />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-2xl font-black text-gray-900 mb-3 group-hover:text-green-600 transition-colors">{benefit.title}</h4>
                                <p className="text-gray-700 font-medium text-lg leading-relaxed">{benefit.description}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {activeTab === 'modules' && (
                <div className="space-y-12">
                  <div className="text-center">
                    <h3 className="text-3xl lg:text-4xl font-black text-gray-900 mb-4">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Specialized Add-on Modules
                      </span>
                    </h3>
                    <p className="text-xl text-gray-600 font-medium max-w-2xl mx-auto">
                      Extend your energy management capabilities with powerful specialized modules
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {addOnModules.map((module, index) => {
                      const IconComponent = module.icon;
                      return (
                        <div key={index} className="group">
                          <div
                            className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 cursor-pointer transform hover:-translate-y-2"
                            onClick={() => setSelectedModule(selectedModule === index ? null : index)}
                          >
                            <div className="flex items-center space-x-4 mb-6">
                              <div className="bg-gradient-to-br from-green-500 to-emerald-600 group-hover:from-emerald-500 group-hover:to-teal-600 p-3 rounded-xl transition-all duration-300 shadow-lg">
                                <IconComponent className="w-7 h-7 text-white" />
                              </div>
                              <h4 className="font-black text-gray-900 text-lg group-hover:text-green-600 transition-colors">{module.title}</h4>
                            </div>

                            <p className="text-gray-700 font-medium mb-6 leading-relaxed">{module.description}</p>

                            {selectedModule === index && (
                              <div className="space-y-3 pt-6 border-t border-green-100 animate-in slide-in-from-top duration-300">
                                {module.features.map((feature, featureIndex) => (
                                  <div key={featureIndex} className="flex items-start space-x-3">
                                    <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                                    <span className="text-gray-700 font-medium">{feature}</span>
                                  </div>
                                ))}
                              </div>
                            )}

                            <div className="flex items-center text-green-600 font-bold mt-6 group-hover:text-emerald-600 transition-colors">
                              <span>{selectedModule === index ? 'Hide Details' : 'View Details'}</span>
                              <ArrowRight className={`w-5 h-5 ml-2 transition-transform duration-300 ${selectedModule === index ? 'rotate-90' : 'group-hover:translate-x-1'}`} />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>

        {/* CTA Section */}
        <div className="py-24 bg-gradient-to-br from-green-600 via-emerald-700 to-teal-800 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-green-500/20 via-emerald-600/30 to-teal-700/20"></div>
            <div className="absolute top-20 left-20 w-40 h-40 bg-green-400/10 rounded-full blur-2xl animate-pulse"></div>
            <div className="absolute bottom-20 right-20 w-60 h-60 bg-emerald-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-teal-400/10 rounded-full blur-xl animate-pulse delay-500"></div>
          </div>

          <div className="relative max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="space-y-8">
              <h2 className="text-4xl lg:text-6xl font-black text-white leading-tight">
                <span className="bg-gradient-to-r from-white via-green-100 to-emerald-200 bg-clip-text text-transparent">
                  Ready to Transform
                </span>
                <br />
                <span className="text-green-200">Your Energy Management?</span>
              </h2>

              <p className="text-xl lg:text-2xl text-green-100 max-w-3xl mx-auto font-medium leading-relaxed">
                Join <span className="text-white font-bold">300+ satisfied customers</span> who have achieved
                <span className="text-green-300 font-bold"> 8-15% energy savings</span> with our intelligent EnMS platform.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
                <button className="group bg-white text-green-700 px-10 py-5 rounded-2xl font-black text-xl hover:bg-green-50 transition-all duration-300 flex items-center justify-center shadow-2xl hover:shadow-3xl transform hover:-translate-y-2">
                  <Phone className="w-6 h-6 mr-3 group-hover:animate-pulse" />
                  Request Consultation
                </button>
                <button className="group border-3 border-white text-white px-10 py-5 rounded-2xl font-black text-xl hover:bg-white hover:text-green-700 transition-all duration-300 flex items-center justify-center backdrop-blur-sm hover:backdrop-blur-none transform hover:-translate-y-2">
                  <Mail className="w-6 h-6 mr-3 group-hover:animate-bounce" />
                  Get Quote
                </button>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    </div>
  );
};

export default AlensoftEnMSProductPage;