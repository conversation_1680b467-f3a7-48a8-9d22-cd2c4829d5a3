"use client";

import React, { useRef, useState } from "react";
import { motion, useInView } from "framer-motion";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import {
  Mail,
  Phone,
  MapPin,
  Navigation,
  Send,
  Building,
  User,
  AtSign,
  MessageSquare,
  Briefcase,
  Bookmark,
  MapPinned,
  FileText,
  Wrench
} from "lucide-react";

const Sales = () => {
  const contactRef = useRef(null);
  const contactInView = useInView(contactRef, { amount: 0.1, once: true });

  // State for form inputs to handle floating labels
  const [formInputs, setFormInputs] = useState({
    name: '',
    email: '',
    company: '',
    designation: '',
    city: '',
    mobile: '',
    pincode: '',
    products: '',
    remarks: ''
  });

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormInputs(prev => ({
      ...prev,
      [id]: value
    }));
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Function to handle map click
  const handleMapClick = () => {
    const address = "No.5, Kumaran St, Pazhvanthangal, Chennai, Tamil Nadu, India, 600114";
    const encodedAddress = encodeURIComponent(address);
    window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`, '_blank');
  };

  return (
    <PageLayout
      title="SALES DEPARTMENT"
      subtitle="Get in touch with our sales team for product information and tailored solutions"
      category="contact"
    >
      <div className="max-w-7xl mx-auto px-4 py-12 relative overflow-hidden">
        {/* Simplified background decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-green-500/5 rounded-full filter blur-3xl"></div>
        <div
          id="contact"
          ref={contactRef}
          className="relative"
        >
          {/* Animated background elements - subtle and non-intrusive */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full opacity-5"
                style={{
                  width: 100 + Math.random() * 200,
                  height: 100 + Math.random() * 200,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  background: i % 3 === 0
                    ? "radial-gradient(circle, rgba(59, 130, 246, 0.6) 0%, rgba(29, 78, 216, 0) 70%)"
                    : i % 3 === 1
                      ? "radial-gradient(circle, rgba(16, 185, 129, 0.6) 0%, rgba(4, 120, 87, 0) 70%)"
                      : "radial-gradient(circle, rgba(245, 158, 11, 0.6) 0%, rgba(180, 83, 9, 0) 70%)",
                }}
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.05, 0.08, 0.05],
                }}
                transition={{
                  duration: 12 + Math.random() * 10,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={contactInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-10"
          >
            <span className="inline-block bg-gradient-to-r from-blue-700 via-green-700 to-yellow-700 text-white px-4 py-1 rounded-full text-sm font-medium mb-3 border border-white/20 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-default">
              Get In Touch
            </span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-blue-900 drop-shadow-md">
              Let's Discuss Your <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-green-600 to-yellow-600 animate-gradient">Energy Needs</span>
            </h2>
            <p className="text-lg text-blue-900 max-w-2xl mx-auto font-medium drop-shadow">
              Our team of specialists is ready to help you find the perfect solution for your energy management challenges.
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={contactInView ? "visible" : "hidden"}
            className="grid grid-cols-1 lg:grid-cols-12 gap-10 items-start"
          >
            {/* Form Section - 7 columns on large screens */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-7 transition-all duration-700 hover:scale-[1.02]"
            >
              <div className="p-8 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 transition-all duration-300 hover:shadow-blue-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>
                <div>
                  <h3 className="text-2xl font-bold mb-6 text-blue-800 drop-shadow">
                    <MessageSquare className="h-5 w-5 mr-2 text-green-600 inline-block" />
                    Enquiry
                  </h3>

                  <form className="space-y-5">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <div className="space-y-2">
                        <label htmlFor="name" className="block text-sm font-medium text-blue-900">
                          Name*
                        </label>
                        <div className="relative group">
                          <div className="flex">
                            <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                              <User className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                            </div>
                            <div className="relative">
                              <input
                                type="text"
                                id="name"
                                value={formInputs.name}
                                onChange={handleInputChange}
                                className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-900 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-blue-400 peer"
                                placeholder="Your name"
                              />
                              <label
                                htmlFor="name"
                                className={`absolute text-sm text-blue-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.name ? 'top-2 scale-75 -translate-y-4' : ''}`}
                              >
                                Your name
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="email" className="block text-sm font-medium text-blue-900">
                          Email*
                        </label>
                        <div className="relative group">
                          <div className="flex">
                            <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                              <AtSign className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                            </div>
                            <div className="relative">
                              <input
                                type="email"
                                id="email"
                                value={formInputs.email}
                                onChange={handleInputChange}
                                className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-900 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-blue-400 peer"
                                placeholder="<EMAIL>"
                              />
                              <label
                                htmlFor="email"
                                className={`absolute text-sm text-blue-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.email ? 'top-2 scale-75 -translate-y-4' : ''}`}
                              >
                                <EMAIL>
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <div className="space-y-2">
                        <label htmlFor="company" className="block text-sm font-medium text-blue-900">
                          Company*
                        </label>
                        <div className="relative group">
                          <div className="flex">
                            <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                              <Building className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                            </div>
                            <div className="relative">
                              <input
                                type="text"
                                id="company"
                                value={formInputs.company}
                                onChange={handleInputChange}
                                className="w-full py-3 px-3 bg-white/90 border border-green-300/70 rounded-md text-blue-900 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 hover:border-green-400 peer"
                                placeholder="Your company"
                              />
                              <label
                                htmlFor="company"
                                className={`absolute text-sm text-green-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.company ? 'top-2 scale-75 -translate-y-4' : ''}`}
                              >
                                Your company
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="designation" className="block text-sm font-medium text-blue-900">
                          Designation*
                        </label>
                        <div className="relative group">
                          <div className="flex">
                            <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                              <Briefcase className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                            </div>
                            <div className="relative">
                              <input
                                type="text"
                                id="designation"
                                value={formInputs.designation}
                                onChange={handleInputChange}
                                className="w-full py-3 px-3 bg-white/90 border border-green-300/70 rounded-md text-blue-900 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 hover:border-green-400 peer"
                                placeholder="Your designation"
                              />
                              <label
                                htmlFor="designation"
                                className={`absolute text-sm text-green-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.designation ? 'top-2 scale-75 -translate-y-4' : ''}`}
                              >
                                Your designation
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <div className="space-y-2">
                        <label htmlFor="city" className="block text-sm font-medium text-blue-900">
                          City*
                        </label>
                        <div className="relative group">
                          <div className="flex">
                            <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                              <MapPinned className="h-5 w-5 text-yellow-600 group-hover:text-yellow-500 transition-colors duration-300" />
                            </div>
                            <div className="relative">
                              <input
                                type="text"
                                id="city"
                                value={formInputs.city}
                                onChange={handleInputChange}
                                className="w-full py-3 px-3 bg-white/90 border border-yellow-300/70 rounded-md text-blue-900 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-all duration-300 hover:border-yellow-400 peer"
                                placeholder="Your city"
                              />
                              <label
                                htmlFor="city"
                                className={`absolute text-sm text-yellow-600 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.city ? 'top-2 scale-75 -translate-y-4' : ''}`}
                              >
                                Your city
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="mobile" className="block text-sm font-medium text-blue-900">
                          Mobile No*
                        </label>
                        <div className="relative group">
                          <div className="flex">
                            <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                              <Phone className="h-5 w-5 text-yellow-600 group-hover:text-yellow-500 transition-colors duration-300" />
                            </div>
                            <div className="relative">
                              <input
                                type="tel"
                                id="mobile"
                                value={formInputs.mobile}
                                onChange={handleInputChange}
                                className="w-full py-3 px-3 bg-white/90 border border-yellow-300/70 rounded-md text-blue-900 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-all duration-300 hover:border-yellow-400 peer"
                                placeholder="Your mobile number"
                              />
                              <label
                                htmlFor="mobile"
                                className={`absolute text-sm text-yellow-600 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.mobile ? 'top-2 scale-75 -translate-y-4' : ''}`}
                              >
                                Your mobile number
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <div className="space-y-2">
                        <label htmlFor="pincode" className="block text-sm font-medium text-blue-900">
                          Pincode*
                        </label>
                        <div className="relative group">
                          <div className="flex">
                            <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                              <MapPin className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                            </div>
                            <div className="relative">
                              <input
                                type="text"
                                id="pincode"
                                value={formInputs.pincode}
                                onChange={handleInputChange}
                                className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-900 placeholder-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-blue-400 peer"
                                placeholder="Your pincode"
                              />
                              <label
                                htmlFor="pincode"
                                className={`absolute text-sm text-blue-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.pincode ? 'top-2 scale-75 -translate-y-4' : ''}`}
                              >
                                Your pincode
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="products" className="block text-sm font-medium text-blue-900">
                          Products Interested in*
                        </label>
                        <div className="relative group">
                          <div className="flex">
                            <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                              <Bookmark className="h-5 w-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                            </div>
                            <div className="relative">
                              <select
                                id="products"
                                value={formInputs.products}
                                onChange={handleInputChange}
                                className="w-full py-3 px-3 bg-white/90 border border-blue-300/70 rounded-md text-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none transition-all duration-300 hover:border-blue-400 peer pt-6"
                              >
                              <option value="">Select product</option>
                              <option value="measure">Measurement Solutions</option>
                              <option value="protect">Protection Systems</option>
                              <option value="conserve">Conservation Technologies</option>
                              <option value="consultation">Energy Consultation</option>
                              </select>
                              <label
                                htmlFor="products"
                                className={`absolute text-sm text-blue-500 duration-300 transform scale-75 top-2 z-10 origin-[0] px-2 left-12`}
                              >
                                Select product
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="remarks" className="block text-sm font-medium text-blue-900">
                        Remarks
                      </label>
                      <div className="relative group">
                        <div className="flex">
                          <div className="flex items-center justify-center w-12 h-12 group-hover:scale-110 transition-transform duration-300">
                            <MessageSquare className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                          </div>
                          <textarea
                            id="remarks"
                            rows={4}
                            value={formInputs.remarks}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-green-300/70 rounded-md text-blue-900 placeholder-blue-400/80 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 hover:border-green-400 resize-none"
                            placeholder="Tell us about your requirements"
                          ></textarea>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="request-demo"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer hover:border-blue-400 transition-colors duration-300"
                        />
                        <label htmlFor="request-demo" className="ml-2 block text-sm text-blue-900 font-medium">
                          Request Demo
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="request-callback"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer hover:border-blue-400 transition-colors duration-300"
                        />
                        <label htmlFor="request-callback" className="ml-2 block text-sm text-blue-900 font-medium">
                          Request Call Back
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="send-details"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer hover:border-blue-400 transition-colors duration-300"
                        />
                        <label htmlFor="send-details" className="ml-2 block text-sm text-blue-900 font-medium">
                          Send Product Details
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="send-updates"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer hover:border-blue-400 transition-colors duration-300"
                        />
                        <label htmlFor="send-updates" className="ml-2 block text-sm text-blue-900 font-medium">
                          Send me information on product updates and new launches.
                        </label>
                      </div>
                    </div>

                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="relative inline-block group w-full"
                    >
                      {/* Button with enhanced gradient glow effect */}
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 via-green-500 to-yellow-500 rounded-lg blur opacity-30 group-hover:opacity-70 transition duration-700 group-hover:duration-200"></div>

                      <Button
                        type="submit"
                        className="relative w-full inline-flex items-center justify-center px-8 py-5 text-lg font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg transition-all duration-300 shadow-lg hover:shadow-blue-500/30 overflow-hidden"
                      >
                        <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-400/20 to-transparent transform -skew-x-30 transition-all duration-300 opacity-0 group-hover:opacity-100"></span>
                        <Send className="h-5 w-5 mr-2 group-hover:translate-x-1 transition-transform duration-300" />
                        <span className="relative z-10 text-base font-bold">Submit</span>
                      </Button>
                    </motion.div>
                  </form>
                </div>
              </div>
            </motion.div>

            {/* Contact Info Section - 5 columns on large screens */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-5 space-y-6"
            >
              {/* Main Contact Info */}
              <div className="transition-all duration-700 hover:scale-[1.02] p-8 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 hover:shadow-blue-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>
                <div>
                  {/* Header with shine effect */}
                  <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-blue-50/50 to-transparent">
                    <h3 className="text-2xl font-bold text-blue-900 mb-4 py-3 px-4 drop-shadow-md">
                      <Mail className="h-5 w-5 mr-2 text-blue-600 inline-block" />
                      Atandra Energy Private Limited
                    </h3>

                    {/* Enhanced shine effect */}
                    <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="col-span-2">
                      <div className="flex items-start text-blue-900 group">
                        <div className="flex items-center mt-1 group-hover:scale-110 transition-transform duration-300">
                          <MapPin className="h-5 w-5 text-green-600 group-hover:text-green-500 transition-colors duration-300" />
                        </div>
                        <div className="ml-2">
                          <p className="text-blue-900 font-bold">No.5, Kumaran St,</p>
                          <p className="text-blue-800">Pazhvanthangal, Tamil Nadu,</p>
                          <p className="text-blue-800">India, Chennai- 600 114.</p>
                        </div>
                      </div>
                    </div>
                    <div className="col-span-1">
                      <motion.div
                        onClick={handleMapClick}
                        className="h-full w-full flex flex-col items-center justify-center cursor-pointer transition-colors bg-green-50/50 rounded-lg hover:bg-green-50 p-2"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Navigation className="h-6 w-6 text-green-600 mb-1" />
                        <span className="text-green-700 text-xs font-medium">View Map</span>
                      </motion.div>
                    </div>
                  </div>

                  <hr className="border-blue-100 mb-6" />

                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <p className="text-blue-900 text-sm mb-1 flex items-center font-medium">
                        <Mail className="h-4 w-4 mr-1 text-blue-600" />
                        Contact Information
                      </p>
                      <a
                        href="mailto:<EMAIL>"
                        className="flex items-center gap-2 text-blue-800 hover:text-blue-600 transition-colors pl-5 font-medium"
                      >
                        <AtSign className="h-4 w-4 text-blue-600" /> <EMAIL>
                      </a>
                      <a
                        href="tel:+919500097966"
                        className="flex items-center gap-2 text-blue-800 hover:text-blue-600 transition-colors pl-5 font-medium"
                      >
                        <Phone className="h-4 w-4 text-blue-600" /> +91 95000 97966
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Department Cards with Links to Dedicated Pages */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <motion.div
                  className="overflow-hidden group transition-all duration-700 hover:scale-[1.02] bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 hover:shadow-blue-100/30 relative cursor-pointer"
                  whileHover={{ y: -5 }}
                  onClick={() => window.location.href = "/contact/sales"}
                >
                  {/* Decorative corner accent */}
                  <div className="absolute -top-8 -right-8 w-16 h-16 bg-blue-500/10 rounded-full blur-lg"></div>
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="p-3 mr-4 bg-blue-100 rounded-full shadow-md group-hover:shadow-blue-200/50 transition-all duration-300 group-hover:scale-110">
                        <Briefcase className="h-6 w-6 text-blue-700 group-hover:text-blue-600 transition-colors duration-300" />
                      </div>
                      <h3 className="text-lg font-bold text-blue-700">Sales Department</h3>
                    </div>
                    <p className="text-blue-800 mb-4 font-medium">For product information and tailored solutions.</p>
                    <div className="mb-4">
                      <div className="flex items-center gap-2 font-medium text-blue-800">
                        <Mail className="h-4 w-4 text-blue-600" /> <EMAIL>
                      </div>
                      <div className="flex items-center gap-2 text-blue-700">
                        <Phone className="h-4 w-4 text-blue-600" /> +91 95000 97966
                      </div>
                    </div>

                    {/* View Details Button */}
                    <div className="mt-4 text-center">
                      <motion.div
                        className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 transition-colors"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span className="mr-2">View Details</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="overflow-hidden group transition-all duration-700 hover:scale-[1.02] bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-green-100/50 hover:border-green-200 hover:shadow-green-100/30 relative cursor-pointer"
                  whileHover={{ y: -5 }}
                  onClick={() => window.location.href = "/contact/service"}
                >
                  {/* Decorative corner accent */}
                  <div className="absolute -top-8 -right-8 w-16 h-16 bg-green-500/10 rounded-full blur-lg"></div>
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="p-3 mr-4 bg-green-100 rounded-full shadow-md group-hover:shadow-green-200/50 transition-all duration-300 group-hover:scale-110">
                        <Wrench className="h-6 w-6 text-green-700 group-hover:text-green-600 transition-colors duration-300" />
                      </div>
                      <h3 className="text-lg font-bold text-green-700">Technical Service</h3>
                    </div>
                    <p className="text-green-800 mb-4 font-medium">24/7 assistance for all our products and services.</p>
                    <div className="mb-4">
                      <div className="flex items-center gap-2 font-medium text-green-800">
                        <Mail className="h-4 w-4 text-green-600" /> <EMAIL>
                      </div>
                      <div className="flex items-center gap-2 text-green-700">
                        <Phone className="h-4 w-4 text-green-600" /> +91 95000 97966
                      </div>
                    </div>

                    {/* View Details Button */}
                    <div className="mt-4 text-center">
                      <motion.div
                        className="inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg shadow-md hover:bg-green-700 transition-colors"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span className="mr-2">View Details</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </div>

            </motion.div>
          </motion.div>

          {/* Map and facility image - clean design without overlays */}
          <div className="mt-16 grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            {/* Company Image - Left side */}
            <motion.div
              className="relative transition-all duration-500 hover:scale-[1.01] flex flex-col"
              whileHover={{ scale: 1.02 }}
            >
              {/* Full width image in a clean card */}
              <div className="overflow-hidden rounded-xl shadow-xl relative group">
                <img
                  src="/background_images/Service-Locations-India.jpeg"
                  alt="KRYKARD Service Locations"
                  className="w-full object-contain transition-transform duration-700 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-blue-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Text below the image */}
              <div className="mt-4 px-2">
                <h3 className="text-lg font-bold text-blue-900 flex items-center">
                  <Building className="h-5 w-5 mr-2 text-blue-700" />
                  Our Facility
                </h3>
                <p className="text-blue-800 text-sm flex items-center">
                  <MapPin className="h-4 w-4 mr-1 text-blue-600 opacity-75" />
                  KRYKARD Headquarters - Chennai, India
                </p>
              </div>
            </motion.div>

            {/* Google Maps - Right side - Clean design without overlays */}
            <motion.div
              className="relative overflow-hidden transition-all duration-500 hover:scale-[1.01] cursor-pointer rounded-xl shadow-xl border border-blue-100/30 group"
              whileHover={{ scale: 1.02 }}
              onClick={() => window.open('https://www.google.com/maps/place/KRYKARD/@13.0963814,80.2622446,15z/data=!4m6!3m5!1s0x3a5260aa8721279f:0x44507a3129269ebe!8m2!3d13.0963814!4d80.2622446!16s%2Fg%2F11j7lq_v6h?entry=ttu', '_blank')}
            >

              <div className="relative">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d22938.9286128844!2d80.2622445948994!3d13.096381397513284!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a5260aa8721279f%3A0x44507a3129269ebe!2sKRYKARD!5e1!3m2!1sen!2sus!4v1744869740527!5m2!1sen!2sus"
                  width="100%"
                  height="400"
                  style={{ border: 0 }}
                  allowFullScreen={true}
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="KRYKARD Location Map"
                  className="z-10"
                ></iframe>

              </div>

              {/* Enhanced Directions button */}
              <div className="absolute bottom-4 right-4 z-30">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white p-3 flex items-center rounded-full shadow-lg border border-blue-100/50 hover:bg-blue-50 transition-all duration-300"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open('https://www.google.com/maps/place/KRYKARD/@13.0963814,80.2622446,15z/data=!4m6!3m5!1s0x3a5260aa8721279f:0x44507a3129269ebe!8m2!3d13.0963814!4d80.2622446!16s%2Fg%2F11j7lq_v6h?entry=ttu', '_blank');
                  }}
                >
                  <Navigation className="h-5 w-5 text-blue-700 mr-2" />
                  <span className="text-blue-800 font-medium">Directions</span>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Add the animations */}
        <style>{`
          @keyframes shine {
            0% {
              left: -100%;
            }
            100% {
              left: 100%;
            }
          }

          @keyframes pulse {
            0%, 100% {
              opacity: 0.2;
            }
            50% {
              opacity: 0.5;
            }
          }

          @keyframes ping {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }
            75%, 100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }

          .animate-shine {
            animation: shine 3s infinite;
          }

          .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          }

          .animate-ping {
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
          }

          /* Button glow animation */
          @keyframes glow {
            0%, 100% {
              box-shadow: 0 0 5px rgba(59, 130, 246, 0.5), 0 0 20px rgba(59, 130, 246, 0.2);
            }
            50% {
              box-shadow: 0 0 15px rgba(59, 130, 246, 0.8), 0 0 40px rgba(59, 130, 246, 0.4);
            }
          }

          .animate-glow {
            animation: glow 2s infinite;
          }

          /* Floating animation for cards - simplified for performance */
          .animate-float {
            /* animation removed for performance */
          }

          /* Subtle breathing animation - removed for performance */
          .animate-breathe {
            /* animation removed for performance */
          }

          /* Button shimmer effect - removed for performance */
          .animate-shimmer {
            /* animation removed for performance */
          }

          /* Gradient shift animation */
          @keyframes gradient-shift {
            0% {
              background-position: 0% 50%;
            }
            50% {
              background-position: 100% 50%;
            }
            100% {
              background-position: 0% 50%;
            }
          }

          .animate-gradient {
            background-size: 200% 200%;
            animation: gradient-shift 5s ease infinite;
          }
        `}</style>
      </div>
    </PageLayout>
  );
};

export default Sales;